const config = require('../config/config');
const logger = require('../config/logger');

class NovuService {
  constructor() {
    this.apiUrl = 'https://api.novu.co/v1/events/trigger';
    this.subscribersUrl = 'https://api.novu.co/v1/subscribers';
    this.apiKey = config.novu.apiKey;
    this.isEnabled = config.novu.enabled;

    if (this.isEnabled) {
      this.init();
    }
  }

  init() {
    try {
      if (!this.apiKey) {
        throw new Error('Novu API key is required when Novu is enabled');
      }

      const queueArg = process.argv.find((arg) => arg.startsWith("--queue="));
      const isNotificationQueue = queueArg && queueArg.includes('notification');
      
      if (isNotificationQueue) {
        logger.info('Novu service initialized successfully with REST API');
      }
      // logger.info('Novu service initialized successfully with REST API');
    } catch (error) {
      logger.error('Failed to initialize Novu service:', error.message);
      this.isEnabled = false;
    }
  }

  isNovuEnabled() {
    return this.isEnabled && this.apiKey;
  }

  async createOrUpdateSubscriber(subscriberId, subscriberData) {
    if (!this.isNovuEnabled()) {
      throw new Error('Novu service is not enabled or initialized');
    }

    try {
      // Prepare subscriber data with subscriberId
      const payload = {
        subscriberId: subscriberId,
        ...subscriberData
      };

      // Use POST to /subscribers for creating/updating (Novu handles both)
      const response = await fetch(this.subscribersUrl, {
        method: 'POST',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Subscriber ${subscriberId} created/updated successfully`);
      return result;
    } catch (error) {
      logger.error(`Error creating/updating subscriber ${subscriberId}:`, error.message);
      throw error;
    }
  }

  // async triggerNotification(workflowId, subscriberId, payload, options = {}) {
  //   if (!this.isNovuEnabled()) {
  //     throw new Error('Novu service is not enabled or initialized');
  //   }

  //   try {
  //     const result = await this.novu.trigger(workflowId, {
  //       to: {
  //         subscriberId: subscriberId,
  //       },
  //       payload: payload,
  //     }, options.transactionId);

  //     logger.info(`Notification triggered successfully for workflow ${workflowId} to subscriber ${subscriberId}`);
  //     return result;
  //   } catch (error) {
  //     logger.error(`Error triggering notification for workflow ${workflowId}:`, error.message);
  //     throw error;
  //   }
  // }

  async sendNotification(notificationData) {
    if (!this.isNovuEnabled()) {
      logger.warn('Novu is disabled, skipping notification');
      return null;
    }

    try {
      const {
        workflowId,
        subscriberId,
        subscriberData,
        payload,
        transactionId
      } = notificationData;

      // Create or update subscriber if subscriber data is provided
      if (subscriberData) {
        await this.createOrUpdateSubscriber(subscriberId, subscriberData);
      }

      // Prepare the trigger request payload
      const requestPayload = {
        name: workflowId,
        to: {
          subscriberId: subscriberId,
        },
        payload: payload,
      };

      if (transactionId) {
        requestPayload.transactionId = transactionId;
      }

      // Trigger the notification via REST API
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `ApiKey ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestPayload)
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
      }

      logger.info(`Notification triggered successfully for workflow ${workflowId} to subscriber ${subscriberId}`);
      return result;
    } catch (error) {
      logger.error('Error sending Novu notification:', error.message);
      throw error;
    }
  }

  async sendDigestNotification(notificationData){
    if (!this.isNovuEnabled()) {
      logger.warn('Novu is disabled, skipping notification');
      return null;
    }
    if(!config.novu.digestWorkflowId){
      logger.warn('Novu digest workflow ID is not configured, skipping notification');
      return null;
    }

    const {transactionId, subscriberId, subscriberData, payload } = notificationData;
    
    return this.sendNotification({
      workflowId: config?.novu?.digestWorkflowId,
      subscriberId,
      subscriberData,
      payload,
      transactionId
    });
  }
}

// Create singleton instance
const novuService = new NovuService();

module.exports = novuService;
