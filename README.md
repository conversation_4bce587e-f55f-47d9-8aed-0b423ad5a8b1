# CareMate Processor

A dynamic, event-driven message processing system built with Node.js that handles various healthcare data processing tasks through RabbitMQ queues. The system processes both internal and external events with comprehensive tracing, performance monitoring, and graceful shutdown capabilities.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Project Structure](#project-structure)
- [Core Components](#core-components)
- [Processors](#processors)
- [Dynamic Configuration](#dynamic-configuration)
- [Installation & Setup](#installation--setup)
- [Usage](#usage)
- [Performance Monitoring](#performance-monitoring)
- [Deployment](#deployment)

## Architecture Overview

The CareMate Processor is designed as a microservice-based event processing system with the following key architectural principles:

### System Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                           CAREMATE PROCESSOR ARCHITECTURE                           │
└─────────────────────────────────────────────────────────────────────────────────────┘

    ┌─────────────────────┐    ┌─────────────────────────────────┐
    │   INTERNAL SOURCES  │    │        RABBITMQ BROKER          │
    ├─────────────────────┤    ├─────────────────────────────────┤
    │ 🎯 Rule Engine     │    │  EXTERNAL EVENT QUEUES:        │
───▶│ 🏥 Processor Project│───▶│  • hl7_queue                   │
    │ 📋 API Project      │    │  • hl7_store                   │
    └─────────────────────┘    │  • hr_csv_data                  │
    ┌─────────────────────┐    │                                 │
    │   EXTERNAL SOURCES  │    │  INTERNAL EVENT QUEUES:         │
    ├─────────────────────┤    │  • patient_admission_procesor   │
    │ 🏥 HL7 Systems      │    │  • notification_queue          │
───▶│ 👥 Extenrla Agent   │───▶│  • email_queue                 │
    │ 🔧 Other Systems    │    │  • text_queue                  │
    └─────────────────────┘    └─────────────────────────────────┘
                                               │
                                               ▼
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              PROCESSOR INSTANCES                                    │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  EXTERNAL PROCESSORS:              │  INTERNAL PROCESSORS:                          │
│  • HL7 Processor                   │  • Add Default Guest                           │
│    --queue=hl7_queue               │    --queue=patient_admission_procesor          │
│  • HL7 Store                       │  • Generate Notification                       │
│    --queue=hl7_store               │    --queue=notification_queue                  │
│  • Parse Data                      │  • Send Email                                  │
│    --queue=hr_csv_data             │    --queue=email_queue                         │
│                                    │  • Send Text                                   │
│                                    │    --queue=text_queue                          │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                                      │
                                                      ▼
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                           MIDDY MIDDLEWARE FRAMEWORK                                │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  EXTERNAL MIDDLEWARE:              │  INTERNAL MIDDLEWARE:                          │
│  • Event Tracing                   │  • Event Action Tracking                       │
│  • Trace ID Generation             │  • Status Management                           │
│  • Performance Monitoring          │  • Error Handling                              │
│  • Audit Trail Creation            │  • Event Lifecycle Management                  │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                                      │
                                                      ▼
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              FUNCTION HANDLERS                                      │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  EXTERNAL FUNCTIONS:               │  INTERNAL FUNCTIONS:                           │
│  • hl7Processor.function.js        │  • addDefaultGuest.function.js                 │
│  • hl7Store.function.js            │  • generateNotification.function.js            │
│  • parseData.function.js           │  • sendEmail.function.js                       │
│                                    │  • sendText.function.js                        │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                                      │
                                                      ▼
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                               DATA & SERVICES                                       │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  🗄️  PostgreSQL Database          │  ⚡ Cache Layer                                │
│  • Event Traces                   │  • Function Mappings                           │
│  • Event Actions                  │  • Event Configuration                         │
│  • Configuration Data             │  • Notification Templates                      │
│  • Business Data                  │  • Application Metadata                        │
│                                    │                                                │
│  📧 External Services              │  🔧 Performance Monitoring                     │
│  • SMTP Server                    │  • Session Tracking                            │
│  • Telnyx SMS API                 │  • Metrics Collection                          │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### Event Flow Types

```
EXTERNAL EVENT FLOW:
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ External    │───▶│ RabbitMQ    │───▶│ External    │───▶│ External    │───▶│ Function  │
│ Sources     │    │ Queue       │    │ Processor   │    │ Middleware  │    │ Handler     │
│ (HL7/Agent) │    │             │    │             │    │ (Tracing)   │    │             │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘

INTERNAL EVENT FLOW:
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Rule Engine │───▶│ RabbitMQ    │───▶│ Internal    │───▶│ Internal    │───▶│ Function    │
│ /CareMate   │    │ Queue       │    │ Processor   │    │ Middleware  │    │ Handler     │
│ System      │    │             │    │             │    │ (Actions)   │    │             │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

### Notification Flow Architecture

```
NOTIFICATION PROCESSING FLOW:
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                           NOTIFICATION EVENT FLOW                                   │
└─────────────────────────────────────────────────────────────────────────────────────┘

STEP 1: RULE ENGINE TRIGGER
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Business Event  │───▶│ JSON Rule       │───▶│ Notification    │
│ (Appointment    │    │ Engine          │    │ Event Generated │
│ Created/Updated)│    │ Evaluation      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘

STEP 2: NOTIFICATION QUEUE PROCESSING
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Notification    │───▶│ notification_   │───▶│ Generate        │
│ Event           │    │ queue           │    │ Notification    │
│                 │    │ (RabbitMQ)      │    │ Processor       │
└─────────────────┘    └─────────────────┘    └─────────────────┘

STEP 3: NOTIFICATION CONFIGURATION LOOKUP
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Generate        │───▶│ Database        │───▶│ Notification    │
│ Notification    │    │ Configuration   │    │ Config          │
│ Function        │    │ Lookup          │    │ Retrieved       │
└─────────────────┘    └─────────────────┘    └─────────────────┘

STEP 4: MULTI-CHANNEL EVENT GENERATION
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Notification    │───▶│ Channel-Specific│───▶│ Multiple Queue  │
│ Config          │    │ Event Generation│    │ Events Created  │
│ (Email + SMS)   │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                ┌─────────────────────────────────────┐
                │     PARALLEL CHANNEL PROCESSING     │
                └─────────────────────────────────────┘
                                │
                ┌───────────────────────────────────────────────────┐
                │                       │                           │
                ▼                       ▼                           ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ email_queue     │    │ text_queue      │    │ push_queue      │
│                 │    │                 │    │ (future)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                │                       │                           │
                ▼                       ▼                           ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Send Email      │    │ Send Text       │    │ Send Push       │
│ Processor       │    │ Processor       │    │ Processor       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                │                       │                           │
                ▼                       ▼                           ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ SMTP Server     │    │ Telnyx SMS API  │    │ Push Service    │
│ Email Delivery  │    │ Text Delivery   │    │ (future)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Detailed Notification Flow Steps

```
NOTIFICATION FLOW BREAKDOWN:

1. TRIGGER PHASE:
   ┌─────────────────────────────────────────────────────────────────────────────────┐
   │ Business Event → Rule Engine → Notification Event                              │
   │                                                                                 │
   │ Example:                                                                        │
   │ • New appointment created                                                       │
   │ • Guest checks in                                                              │
   │ • Appointment cancelled                                                        │
   │ • Patient information updated                                                  │
   └─────────────────────────────────────────────────────────────────────────────────┘

2. QUEUE ROUTING PHASE:
   ┌─────────────────────────────────────────────────────────────────────────────────┐
   │ Event → notification_queue → generateNotification Function                     │
   │                                                                                 │
   │ Event Structure:                                                               │
   │ {                                                                              │
   │   "event_id": "uuid",                                                          │
   │   "event_type": "notification",                                                │
   │   "params": {                                                                  │
   │     "notification": "Send Check In notification",                              │
   │     "data": { appointment_guest_id: "uuid" }                                   │
   │   }                                                                            │
   │ }                                                                              │
   └─────────────────────────────────────────────────────────────────────────────────┘

3. CONFIGURATION LOOKUP PHASE:
   ┌─────────────────────────────────────────────────────────────────────────────────┐
   │ Function → Database → Notification Configuration                                │
   │                                                                                 │
   │ Configuration Example:                                                         │
   │ {                                                                              │
   │   "name": "Send Check In notification",                                        │
   │   "schema": "PatientAppointmentGuestView",                                     │
   │   "schema_column": "appointment_guest_id",                                     │
   │   "channels": [                                                                │
   │     { "channel": "email", "template_id": "checkin_email" },                    │
   │     { "channel": "text", "template_id": "checkin_sms" }                        │
   │   ]                                                                            │
   │ }                                                                              │
   └─────────────────────────────────────────────────────────────────────────────────┘

4. CHANNEL EVENT GENERATION PHASE:
   ┌─────────────────────────────────────────────────────────────────────────────────┐
   │ For Each Channel → Generate Specific Event → Route to Channel Queue            │
   │                                                                                 │
   │ Email Event:                          Text Event:                              │
   │ {                                     {                                         │
   │   "event_type": "email",              │   "event_type": "text",                │
   │   "queue": "email_queue",             │   "queue": "text_queue",               │
   │   "notification": {                   │   "notification": {                    │
   │     "template": "checkin_email",      │     "template": "checkin_sms",         │
   │     "data": { ... }                   │     "data": { ... }                    │
   │   }                                   │   }                                     │
   │ }                                     │ }                                       │
   └─────────────────────────────────────────────────────────────────────────────────┘

5. CHANNEL PROCESSING PHASE:
   ┌─────────────────────────────────────────────────────────────────────────────────┐
   │ Channel Queue → Channel Processor → External Service → Delivery                │
   │                                                                                 │
   │ Email Flow:                           Text Flow:                                │
   │ email_queue → sendEmail Function      text_queue → sendText Function           │
   │ → Template Rendering                  → Template Processing                     │
   │ → SMTP Server                         → Telnyx API                             │
   │ → Email Delivery                      → SMS Delivery                           │
   └─────────────────────────────────────────────────────────────────────────────────┘
```

### Notification Configuration Examples

```javascript
// Database notification configuration
{
  "name": "Send Check In notification",
  "schema": "PatientAppointmentGuestView",     // Data source view/table
  "schema_column": "appointment_guest_id",     // Primary key for data lookup
  "description": "Send check-in notification to patient on guest arrival",
  "status": "Active",
  "language": "en-US",
  "channels": [
    {
      "channel": "email",
      "template_id": "checkin_email_template",
      "priority": 1
    },
    {
      "channel": "text",
      "template_id": "checkin_sms_template",
      "priority": 2
    }
  ]
}

// Rule engine configuration for triggering notifications
{
  "conditions": {
    "all": [
      {"fact": "event_type", "operator": "equal", "value": "guest_checkin"},
      {"fact": "appointment.notification_enabled", "operator": "equal", "value": true}
    ]
  },
  "event": {
    "type": "notification",
    "params": {
      "notification": "Send Check In notification",
      "queue": "notification_queue"
    }
  }
}
```

### Event-Driven Architecture
- **RabbitMQ Integration**: All processors listen to specific queues for incoming events
- **Dynamic Queue Management**: Queue configurations are stored in database and cached for performance
- **Event Tracing**: Comprehensive tracing system for both internal and external events
- **Collection-Based Processing**: Messages are collected for configurable timeouts before batch processing

### Dynamic Configuration
- **Database-Driven**: All processor configurations, mappings, and rules stored in database
- **Caching Layer**: Multi-level caching (Redis/Memcached/Memory) for performance optimization
- **Rule Engine**: JSON-based rules engine for dynamic event routing and processing logic
- **Hot Reloading**: Configuration changes take effect without service restart through cache invalidation

### Middleware Architecture
- **Middy Framework**: Leverages Middy for middleware composition and error handling
- **Dual Middleware Types**:
  - **Internal Middleware**: For events generated within the system
  - **External Middleware**: For events from external sources with comprehensive tracing
- **Performance Monitoring**: Built-in performance tracking for every operation

### Scalability & Reliability
- **Lambda-like Function Scaling**: Each processor function can be scaled independently like AWS Lambda functions, rather than traditional microservices
- **Horizontal Scaling**: Multiple instances of each processor can run independently for high availability
- **Concurrency Control**: Configurable message processing limits with p-limit for optimal resource utilization
- **Graceful Shutdown**: Ensures message integrity during service restarts
- **Error Handling**: Comprehensive error handling with dead letter queue support

## Project Structure

```
processor/
├── config/                     # Configuration files
│   ├── config.js              # Main configuration with environment validation
│   ├── database.js            # Database connection configuration
│   ├── rabbitmq.js           # RabbitMQ connection setup
│   ├── logger.js             # Winston logging configuration
│   ├── caching.js            # Multi-driver caching configuration
│   └── sequelize.js          # Sequelize ORM configuration
├── functions/                  # Processor implementations
│   ├── hl7Processor.function.js      # HL7 message processing (external)
│   ├── hl7Store.function.js          # HL7 raw storage (external)
│   ├── addDefaultGuest.function.js   # Add patient guests (internal)
│   ├── generateNotification.function.js # Notification generation (internal)
│   ├── sendEmail.function.js         # Email sending (internal)
│   ├── sendText.function.js          # SMS sending (internal)
│   ├── parseData.function.js         # Agent data parsing (external)
│   └── index.js              # Dynamic function loader
├── middlewares/               # Middleware implementations
│   ├── internalMiddleware.js  # Internal event processing middleware
│   ├── externalMiddleware.js  # External event tracing middleware
│   └── index.js              # Middleware exports
├── models/                    # Database models (50+ models)
│   ├── event.model.js        # Event definitions
│   ├── eventConfig.model.js  # Event configuration
│   ├── eventTrace.model.js   # Event tracing
│   ├── function.model.js     # Function definitions
│   ├── notification.model.js # Notification configurations
│   └── ... (other models)
├── seeders/                   # Database seeders for dynamic configuration
│   ├── **************-function-seeder.js     # Function-to-queue mappings
│   ├── **************-event-config-seeder.js # Event routing configuration
│   ├── **************-notification-seeder.js # Notification templates
│   └── ... (other seeders)
├── mappings/                  # Data transformation mappings
│   ├── hl7Message.mapping.json       # HL7 field mappings
│   ├── hl7TypeStatus.mapping.json    # HL7 event type mappings
│   └── hrData.mapping.json           # HR data mappings
├── helpers/                   # Utility functions
│   ├── caching.helper.js     # Caching utilities with multi-level support
│   ├── hl7.helper.js         # HL7 parsing and transformation
│   ├── global.helper.js      # Global utility functions
│   └── ... (other helpers)
├── services/                  # Business logic services
│   ├── event.service.js      # Event processing and RabbitMQ operations
│   ├── performance.service.js # Performance monitoring
│   └── ... (other services)
├── migrations/               # Database migrations
├── views/                    # Database views
├── validations/              # Input validation schemas
├── performances/             # Performance monitoring logs
├── index.js                  # Main application entry point
├── package.json             # Dependencies and scripts
└── ecosystem.config.js      # PM2 deployment configuration
```

## Core Components

### 1. Main Application (index.js)
The main entry point orchestrates the entire processing pipeline:

- **Queue-Based Processing**: Accepts `--queue=<queueName>` parameter to specify which queue to process
- **Dynamic Function Loading**: Automatically loads and maps processor functions based on database configuration
- **Batch Processing**: Collects messages for configurable timeout periods before processing in batches
- **Concurrency Control**: Uses p-limit for controlling concurrent message processing
- **Graceful Shutdown**: Handles SIGTERM/SIGINT signals to ensure message integrity
- **Performance Monitoring**: Comprehensive performance tracking for every operation

### 2. Middleware System
Two distinct middleware types handle different event sources:

#### Internal Middleware (`internalMiddleware.js`)
- Processes events generated within the CareMate system
- Tracks event actions in `event_actions` table
- Provides before/after/error hooks for comprehensive logging
- Manages event status transitions (pending → processing → completed/failed)

#### External Middleware (`externalMiddleware.js`)
- Handles events from external sources (HL7, agent data, etc.)
- Creates comprehensive event traces in `event_traces` table
- Generates unique trace IDs for end-to-end tracking
- Links trace actions for detailed audit trails

### 3. Caching System
Multi-level caching architecture for optimal performance:

- **Cache Drivers**: Redis, Memcached, or in-memory caching
- **Function Caching**: Caches function-to-queue mappings for 10 hours
- **Event Configuration Caching**: Caches event routing rules
- **Notification Configuration Caching**: Caches notification templates and settings
- **Application Type Caching**: Caches application metadata

### 4. Performance Monitoring
Built-in performance monitoring system:

- **Session-Based Tracking**: Each processing session gets unique performance tracking
- **Metric Collection**: Tracks processing times, throughput, success rates
- **File-Based Logging**: Stores performance data in timestamped log files
- **Real-Time Metrics**: Provides real-time performance insights during processing

## Processors

The system includes 7 main processors, each handling specific types of events:

### 1. HL7 Processor (External)
**Queue**: `hl7_queue`
**Type**: External Event Processor
**Function**: `hl7Processor`

Processes HL7 messages from external healthcare systems and maps them to the internal database schema.

#### Features:
- **Multi-Format Support**: Handles ADT (Admission/Discharge/Transfer) and SIU (Scheduling) message types
- **Dynamic Mapping**: Uses JSON-based mapping configuration for field transformations
- **Event Type Mapping**: Maps HL7 event codes to internal appointment statuses
- **Validation**: Comprehensive input validation using Joi schemas
- **Transaction Safety**: All operations wrapped in database transactions

#### Supported HL7 Message Types:
- **ADT Messages**: A01 (Admit), A02 (Transfer), A03 (Discharge), A04 (Registration), A08 (Update), A11 (Cancel Admit), A12 (Cancel Transfer), A13 (Cancel Discharge)
- **SIU Messages**: S12 (New Appointment), S13 (Appointment Rescheduling), S14 (Appointment Modification), S15 (Appointment Cancellation), S17 (Appointment Deletion)

#### Configuration:
```json
// Example mapping from hl7Message.mapping.json
{
  "MSH.9.1": "message_type",
  "MSH.9.2": "event_code",
  "PID.3.1": "patient_id",
  "PID.5.1": "patient_last_name",
  "PID.5.2": "patient_first_name"
}
```

### 2. HL7 Store (External)
**Queue**: `hl7_store`
**Type**: External Event Processor
**Function**: `hl7Store`

Stores raw HL7 messages in the database without processing for audit and compliance purposes.

#### Features:
- **Raw Storage**: Preserves original HL7 message format
- **Metadata Extraction**: Extracts basic message metadata (type, timestamp, source)
- **Compliance**: Maintains audit trail for regulatory requirements
- **Fast Processing**: Minimal processing overhead for high-throughput scenarios

### 3. Add Default Guest (Internal)
**Queue**: `patient_admission_procesor`
**Type**: Internal Event Processor
**Function**: `addDefaultGuest`

Automatically adds existing patient guests to new appointments through rule engine triggers.

#### Features:
- **Automatic Guest Addition**: Adds all non-denied guests of a patient to new appointments
- **Guest PIN Generation**: Generates unique 6-digit PINs for guest access
- **Status Management**: Sets appropriate guest status (registered)
- **Bulk Operations**: Efficient bulk insertion for multiple guests

#### Trigger Conditions:
- New appointment creation
- Appointment modification with guest list changes
- Patient guest relationship updates

#### Configuration Example:
```javascript
// Triggered by rule engine when new appointment is created
{
  "event_id": "uuid",
  "parent_id": "appointment_id",
  "event_type": "appointment_created",
  "params": {
    "facility_id": "facility_uuid",
    "screening_required": true
  }
}
```

### 4. Generate Notification (Internal)
**Queue**: `notification_queue`
**Type**: Internal Event Processor
**Function**: `generateNotification`

Generates notifications based on configurable templates and routes them to appropriate channels.

#### Features:
- **Multi-Channel Support**: Supports email, SMS, push notifications
- **Template-Based**: Uses database-stored templates for dynamic content
- **Rule-Based Triggering**: Triggered by rule engine based on various system events
- **Channel Configuration**: Each notification can specify multiple delivery channels

#### Notification Flow:
1. Rule engine triggers notification event
2. Processor loads notification configuration from database
3. For each configured channel, creates specific delivery event
4. Routes to appropriate channel processor (email/SMS)

#### Configuration Example:
```javascript
// Notification configuration in database
{
  "name": "Send Check In notification",
  "schema": "PatientAppointmentGuestView",
  "channels": [
    {"channel": "email", "template_id": "checkin_email"},
    {"channel": "text", "template_id": "checkin_sms"}
  ]
}
```

### 5. Send Email (Internal)
**Queue**: `email_queue`
**Type**: Internal Event Processor
**Function**: `sendEmail`

Sends email notifications using configurable SMTP settings and dynamic templates.

#### Features:
- **Template Engine**: EJS-based email templates with dynamic data injection
- **SMTP Configuration**: Configurable email service settings
- **Attachment Support**: Supports file attachments for documents
- **Delivery Tracking**: Tracks email delivery status and failures
- **Retry Logic**: Automatic retry for failed deliveries

#### Email Template Structure:
- **Subject Templates**: Dynamic subject line generation
- **HTML Templates**: Rich HTML email content
- **Text Templates**: Plain text fallback
- **Variable Substitution**: Dynamic content based on event data

### 6. Send Text (Internal)
**Queue**: `text_queue`
**Type**: Internal Event Processor
**Function**: `sendText`

Sends SMS notifications using Telnyx API with template-based messaging.

#### Features:
- **Telnyx Integration**: Uses Telnyx API for SMS delivery
- **Template-Based**: Database-stored SMS templates
- **International Support**: Supports international phone number formats
- **Delivery Tracking**: Tracks SMS delivery status
- **Cost Optimization**: Efficient message batching and routing

#### SMS Configuration:
```javascript
// Telnyx configuration
{
  "TELNYX_API_KEY": "your_api_key",
  "TELNYX_PHONE_NUMBER": "+**********",
  "message_template": "Hello {{patient_name}}, your appointment is confirmed for {{appointment_date}}"
}
```

### 7. Parse Data (External)
**Queue**: `hr_csv_data`
**Type**: External Event Processor
**Function**: `parseData`

Processes data from external agents (HR systems, identity providers) and maps them to internal database schema.

#### Features:
- **Multi-Format Support**: Handles CSV, JSON, XML data formats
- **Dynamic Mapping**: Configurable field mapping based on data source
- **Data Validation**: Comprehensive validation before database insertion
- **Batch Processing**: Efficient processing of large datasets
- **Error Handling**: Detailed error reporting for data quality issues

#### Supported Data Types:
- **HR Data**: Employee information, organizational structure
- **Identity Data**: User profiles, access credentials
- **Facility Data**: Building, room, and location information

#### Mapping Configuration:
```json
// Example HR data mapping
{
  "employee_id": "identity.external_id",
  "first_name": "identity.first_name",
  "last_name": "identity.last_name",
  "email": "identity.email",
  "department": "identity.department"
}
```

## Dynamic Configuration

The CareMate Processor system is built with dynamic configuration at its core, allowing for runtime changes without service restarts.

### Function-to-Queue Mapping
All processor functions are dynamically mapped to RabbitMQ queues through database configuration:

```javascript
// From function-seeder.js
{
  "queue": "hl7_queue",
  "type": "1",  // 1 = external, 0 = internal
  "name": "hl7Processor",
  "display_name": "HL7 Processor",
  "application_type_id": "processor_app_id"
}
```

### Event Configuration
Event routing and processing rules are stored in the database:

```javascript
// From event-config-seeder.js
{
  "event_name": "Email Notification Event",
  "event": "email",
  "queue": "email_queue",
  "order": 1  // Processing priority
}
```

### Notification Configuration
Notification templates and channel configurations are database-driven:

```javascript
// Notification configuration
{
  "name": "Send Check In notification",
  "schema": "PatientAppointmentGuestView",  // Data source
  "schema_column": "appointment_guest_id",  // Primary key
  "channels": [
    {"channel": "email", "template": "checkin_email.ejs"},
    {"channel": "text", "template": "checkin_sms.txt"}
  ]
}
```

### Rule Engine Integration
The system uses a JSON-based rules engine for dynamic event triggering:

```javascript
// Example rule for triggering guest addition
{
  "conditions": {
    "all": [
      {"fact": "event_type", "operator": "equal", "value": "appointment_created"},
      {"fact": "appointment.screening", "operator": "equal", "value": true}
    ]
  },
  "event": {
    "type": "add_default_guests",
    "params": {"queue": "patient_admission_procesor"}
  }
}
```

### Mapping Configurations
Data transformation mappings are stored as JSON files and can be updated without code changes. The mapping system provides a flexible way to transform external data formats into internal database schema.

#### Mapping Architecture

```
MAPPING FLOW:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ External Data   │───▶│ Mapping Config  │───▶│ Transformation  │───▶│ Database Model  │
│ (HL7/CSV/JSON)  │    │ (JSON Files)    │    │ Engine          │    │ (Sequelize)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

#### HL7 Message Mapping (`hl7Message.mapping.json`)
Maps HL7 message fields to database model properties:

```json
{
  "MSH.9.1": "message_type",           // Maps to Hl7Message.message_type
  "MSH.9.2": "event_code",             // Maps to Hl7Message.event_code
  "PID.3.1": "patient_id",             // Maps to Patient.patient_id
  "PID.5.1": "patient_last_name",      // Maps to Patient.last_name
  "PID.5.2": "patient_first_name",     // Maps to Patient.first_name
  "PID.7": "date_of_birth",            // Maps to Patient.date_of_birth
  "PV1.2": "patient_class",            // Maps to Appointment.patient_class
  "PV1.3.1": "assigned_patient_location", // Maps to Appointment.location
  "PV1.44": "admit_date_time",         // Maps to Appointment.scheduled_date
  "PV1.45": "discharge_date_time"      // Maps to Appointment.end_date
}
```

**Database Models Involved:**
- `Hl7Message` model: Stores raw HL7 message metadata
- `Patient` model: Patient demographic information
- `PatientIdentifier` model: Patient identification numbers
- `Appointment` model: Appointment scheduling information

#### HL7 Type Status Mapping (`hl7TypeStatus.mapping.json`)
Maps HL7 event codes to internal appointment statuses and actions:

```json
{
  "ADT_TYPE_STATUS_MAP": {
    "A01": {"status": "admitted", "action": "create"},      // New admission
    "A02": {"status": "transferred", "action": "update"},   // Patient transfer
    "A03": {"status": "discharged", "action": "update"},    // Patient discharge
    "A04": {"status": "registered", "action": "create"},    // Patient registration
    "A08": {"status": "updated", "action": "update"},       // Patient info update
    "A11": {"status": "cancelled", "action": "cancel"},     // Cancel admission
    "A12": {"status": "cancelled", "action": "cancel"},     // Cancel transfer
    "A13": {"status": "cancelled", "action": "cancel"}      // Cancel discharge
  },
  "SIU_TYPE_STATUS_MAP": {
    "S12": {"status": "scheduled", "action": "create"},     // New appointment
    "S13": {"status": "rescheduled", "action": "update"},   // Reschedule appointment
    "S14": {"status": "modified", "action": "update"},      // Modify appointment
    "S15": {"status": "cancelled", "action": "cancel"},     // Cancel appointment
    "S17": {"status": "deleted", "action": "delete"}        // Delete appointment
  }
}
```

**Database Models Involved:**
- `Appointment` model: Updates appointment status
- `EventTrace` model: Records processing actions
- `TraceAction` model: Detailed action logging

#### HR Data Mapping (`hrData.mapping.json`)
Maps HR system data to identity and organizational models:

```json
{
  "employee_id": "Identity.external_id",        // Maps to model: Identity  on column: external_id
  "first_name": "Identity.first_name",          // Maps to model: Identity  on column: first_name
  "last_name": "Identity.last_name",            // Maps to model: Identity  on column: last_name
  "email": "Identity.email",                    // Maps to model: Identity  on column: email
  "department": "Identity.department",          // Maps to model: Identity  on column: department
  "job_title": "Identity.job_title",            // Maps to model: Identity  on column: job_title
  "manager_id": "Identity.manager_id",          // Maps to model: Identity  on column: manager_id
  "hire_date": "Identity.hire_date",            // Maps to model: Identity  on column: hire_date
  "status": "Identity.status",                  // Maps to model: Identity  on column: status
  "building_code": "Building.building_code",    // Maps to model: Building  on column: code
  "floor_number": "Floor.floor_number",      // Maps to model: Floor  on column: number
  "room_number": "Room.room_number"         // Maps to model: Room  on column: number
}
```

**Database Models Involved:**
- `Identity` model: User identity information
- `Building` model: Building information
- `Floor` model: Floor details
- `Room` model: Room assignments
- `StagingData` model: Temporary data storage during processing

#### Mapping Processing Logic

```
MAPPING PROCESSING STEPS:
1. Load Mapping Configuration
   ├── Read JSON mapping file
   ├── Cache mapping rules
   └── Validate mapping structure

2. Parse Input Data
   ├── Extract fields based on data format
   ├── Apply data type conversions
   └── Handle missing/null values

3. Transform Data
   ├── Apply field mappings
   ├── Execute transformation rules
   └── Validate transformed data

4. Store in Database
   ├── Create/Update model instances
   ├── Handle relationships
   └── Commit transaction
```

#### Dynamic Mapping Management
- **File-Based Configuration**: Mappings stored as JSON files for easy modification
- **Runtime Loading**: Mappings loaded dynamically during processing
- **Validation**: Automatic validation of mapping structure and data types
- **Error Handling**: Comprehensive error handling for mapping failures
- **Versioning**: Support for multiple mapping versions for backward compatibility

### Caching Strategy
The dynamic configuration system uses multi-level caching for optimal performance:

1. **Function Caching**: 10-hour TTL for function-to-queue mappings
2. **Event Configuration Caching**: 10-hour TTL for event routing rules
3. **Notification Caching**: 1-hour TTL for notification templates
4. **Application Caching**: 24-hour TTL for application metadata

### Configuration Hot Reloading
Changes to database configuration are automatically picked up through cache invalidation:

```javascript
// Cache invalidation triggers
- Function configuration changes → Clear function cache
- Event configuration changes → Clear event config cache
- Notification template changes → Clear notification cache
- Mapping file changes → Restart required (file-based)
```

## Message Processing Architecture

### Collection-Based Processing Approach

The CareMate Processor uses a sophisticated collection-based approach for optimal performance and resource utilization:

#### Collection Flow Diagram

```
MESSAGE COLLECTION FLOW:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Message Arrives │───▶│ Add to          │───▶│ Collection      │───▶│ Batch Process   │
│ in Queue        │    │ Collection      │    │ Timer Check     │    │ All Messages    │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Reset Collection│    │ Trigger         │
                       │ Timer           │    │ Processing      │
                       └─────────────────┘    └─────────────────┘
```

#### How Collection Works

1. **Message Arrival**: When a message arrives in a RabbitMQ queue
2. **Collection Addition**: Message is added to an in-memory collection array
3. **Timer Management**: A collection timer is set/reset for the configured timeout
4. **Batch Trigger**: When timer expires, all collected messages are processed together
5. **Parallel Processing**: Messages in the collection are processed concurrently

#### Collection Configuration

```javascript
// Configuration parameters
COLLECTION_TIMEOUT = 3000;  // 3 seconds - wait time before processing collection
CONCURRENCY_LIMIT = 15;     // Maximum concurrent message processing
SHUTDOWN_TIMEOUT = 30000;   // 30 seconds - graceful shutdown timeout
```

#### Collection Benefits

```
ADVANTAGES OF COLLECTION APPROACH:
┌─────────────────────────────────────────────────────────────────────────────────────┐
│ 🚀 PERFORMANCE BENEFITS:                                                            │
│ • Reduced database connection overhead                                              │
│ • Batch database operations                                                         │
│ • Optimized resource utilization                                                   │
│ • Better throughput for high-volume scenarios                                      │
│                                                                                     │
│ 📊 MONITORING BENEFITS:                                                             │
│ • Collection-level performance metrics                                             │
│ • Batch success/failure rates                                                      │
│ • Comprehensive performance logging                                                │
│ • Real-time throughput tracking                                                    │
│                                                                                     │
│ 🛡️ RELIABILITY BENEFITS:                                                            │
│ • Graceful shutdown handling                                                       │
│ • Message state tracking                                                           │
│ • Error isolation per collection                                                   │
│ • Automatic retry mechanisms                                                       │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### Concurrency Control System

The system implements sophisticated concurrency control to balance performance and resource usage:

#### Concurrency Architecture

```
CONCURRENCY CONTROL FLOW:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Message         │───▶│ p-limit         │───▶│ Concurrent      │───▶│ Resource        │
│ Collection      │    │ Queue           │    │ Processing      │    │ Management      │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Concurrency     │    │ Active Promise  │
                       │ Limit Control   │    │ Tracking        │
                       └─────────────────┘    └─────────────────┘
```

#### Concurrency Implementation

```javascript
// Concurrency control using p-limit
const CONCURRENCY_LIMIT = 15;  // Maximum concurrent operations
const pLimitFn = pLimit(CONCURRENCY_LIMIT);

// Processing with concurrency control
const processingPromises = collection.map(({ msg, event }, index) => {
  const processMessage = async () => {
    // Message processing logic
    const handler = wrapDynamicHandler(dynamicFunction, middleware);
    await handler(event, context);
  };

  // Apply concurrency limit
  if (pLimitFn) {
    return pLimitFn(processMessage);
  } else {
    return processMessage();
  }
});

// Process all messages with controlled concurrency
const results = await Promise.allSettled(processingPromises);
```

#### Concurrency Benefits and Trade-offs

```
CONCURRENCY ANALYSIS:
┌─────────────────────────────────────────────────────────────────────────────────────┐
│ ✅ BENEFITS:                                                                        │
│ • Improved throughput for I/O-bound operations                                     │
│ • Better resource utilization                                                      │
│ • Reduced overall processing time                                                  │
│ • Scalable performance based on system capacity                                    │
│                                                                                     │
│ ⚖️ TRADE-OFFS:                                                                      │
│ • Increased memory usage during processing                                         │
│ • Database connection pool pressure                                                │
│ • Complexity in error handling and state management                                │
│ • Need for careful resource monitoring                                             │
│                                                                                     │
│ 🎛️ CONFIGURATION GUIDELINES:                                                        │
│ • Low-resource systems: CONCURRENCY_LIMIT = 5-10                                   │
│ • Medium systems: CONCURRENCY_LIMIT = 10-20                                        │
│ • High-performance systems: CONCURRENCY_LIMIT = 20-50                              │
│ • Monitor CPU, memory, and database connections                                    │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

#### Message State Tracking

```javascript
// Message states during processing
const MESSAGE_STATES = {
  PENDING: 'pending',      // Message received, waiting for processing
  PROCESSING: 'processing', // Currently being processed
  SUCCESSFUL: 'successful', // Successfully processed
  FAILED: 'failed'         // Processing failed
};

// State tracking during collection processing
const messageStates = new Map(); // messageId -> state

// State transitions
messageStates.set(messageId, MESSAGE_STATES.PENDING);     // Initial state
messageStates.set(messageId, MESSAGE_STATES.PROCESSING);  // Start processing
messageStates.set(messageId, MESSAGE_STATES.SUCCESSFUL); // Success
messageStates.set(messageId, MESSAGE_STATES.FAILED);     // Failure
```

#### Performance Monitoring Integration

```
PERFORMANCE MONITORING WITH COLLECTIONS:
┌─────────────────────────────────────────────────────────────────────────────────────┐
│ 📊 COLLECTION METRICS:                                                              │
│ • Collection ID: Unique identifier for each batch                                  │
│ • Collection Size: Number of messages in batch                                     │
│ • Processing Time: Total time for entire collection                                │
│ • Success Rate: Percentage of successful messages                                  │
│ • Throughput: Messages processed per second                                        │
│                                                                                     │
│ 📈 INDIVIDUAL MESSAGE METRICS:                                                      │
│ • Message Processing Time: Time per individual message                             │
│ • Queue Wait Time: Time message spent in queue                                     │
│ • Database Operation Time: Time for database operations                            │
│ • External API Time: Time for external service calls                              │
│                                                                                     │
│ 🔍 PERFORMANCE ANALYSIS:                                                            │
│ • Average, Min, Max processing times                                               │
│ • Performance trends over time                                                     │
│ • Resource utilization patterns                                                    │
│ • Error rate analysis                                                              │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### Graceful Shutdown with Collection Processing

The system implements sophisticated graceful shutdown mechanisms that work seamlessly with the collection-based processing approach:

#### Shutdown Flow Diagram

```
GRACEFUL SHUTDOWN FLOW:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Shutdown Signal │───▶│ Stop Accepting  │───▶│ Process Pending │───▶│ Wait for Active │
│ (SIGTERM/SIGINT)│    │ New Messages    │    │ Collections     │    │ Processing      │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │                        │
                                ▼                        ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
                       │ Cancel RabbitMQ │    │ Clear Collection│    │ Handle Remaining│
                       │ Consumer        │    │ Timer           │    │ Messages        │
                       └─────────────────┘    └─────────────────┘    └─────────────────┘
```

#### Shutdown Process Steps

1. **Signal Reception**: Process receives SIGTERM, SIGINT, or SIGHUP
2. **Consumer Cancellation**: Stop accepting new messages from RabbitMQ
3. **Collection Processing**: Process any remaining messages in current collection
4. **Active Processing Wait**: Wait for currently processing messages to complete
5. **Message State Handling**: Handle messages based on their processing state
6. **Resource Cleanup**: Close database connections and RabbitMQ channels

#### Message State Handling During Shutdown

```javascript
// Message handling during shutdown based on state
const handleRemainingMessages = async () => {
  for (const [messageId, state] of messageStates.entries()) {
    switch (state) {
      case MESSAGE_STATES.PENDING:
        // Unprocessed messages - nack without requeue (remove from queue)
        channel.nack(msg, false, false);
        break;

      case MESSAGE_STATES.PROCESSING:
        // Currently processing - wait for completion or timeout
        // Then nack with requeue for retry
        channel.nack(msg, false, true);
        break;

      case MESSAGE_STATES.FAILED:
        // Failed messages - nack with requeue for retry
        channel.nack(msg, false, true);
        break;

      case MESSAGE_STATES.SUCCESSFUL:
        // Already acknowledged during processing
        // No action needed
        break;
    }
  }
};
```

#### Shutdown Configuration

```javascript
// Shutdown timeout configuration
SHUTDOWN_TIMEOUT = 30000; // 30 seconds maximum wait time

// Shutdown behavior
- Wait for active processing to complete
- Process remaining collections
- Handle message states appropriately
- Ensure no message loss during shutdown
```

## Installation & Setup

### Prerequisites
- Node.js 18+
- PostgreSQL 12+
- RabbitMQ 3.8+
- Redis (optional, for caching)

### Environment Configuration
Create environment file (`.env`, `.env.dev`, or `.env.local`):

```bash
# Application
NODE_ENV=development
PORT=3000

# Database Configuration
DB_DIALECT=postgres
DB_WRITE_HOST=localhost
DB_WRITE_USERNAME=postgres
DB_WRITE_PASSWORD=password
DB_WRITE_DATABASE=caremate_processor
DB_READ_HOST=localhost
DB_READ_USERNAME=postgres
DB_READ_PASSWORD=password
DB_READ_DATABASE=caremate_processor
DB_LOGGING=false

# RabbitMQ Configuration
MESSAGE_QUEUING=true
RABBITMQ_URL=amqp://localhost:5672

# Message Processing Configuration
CONCURRENCY_LIMIT=15
COLLECTION_TIMEOUT=3000
SHUTDOWN_TIMEOUT=30000

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_ACCESS_EXPIRATION_MINUTES=30
JWT_REFRESH_EXPIRATION_DAYS=30

# Logging Configuration
LOG_LEVEL=info
LOG_MAX_SIZE=20m
LOG_MAX_FILES=30d
LOG_FILE_TRANSPORT=false

# Caching Configuration
CACHE_DRIVER=memory  # redis, memcached, or memory
CACHE_TTL=60
REDIS_HOST=localhost
REDIS_PORT=6379

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# SMS Configuration (Telnyx)
TELNYX_API_KEY=your_telnyx_api_key
TELNYX_PHONE_NUMBER=+**********
```

### Installation Steps

1. **Clone and Install Dependencies**
```bash
git clone https://git.onetalkhub.com/care/processor.git
cd processor
npm install
```

2. **Database Setup**
```bash
# Sync database schema
npm run db

# Run migrations
npx sequelize-cli db:migrate

# Seed initial data
npx sequelize-cli db:seed:all
```

3. **Environment-Specific Setup**
```bash
# For development environment
npm run db -- --env-file .env.dev
npx sequelize-cli db:seed:all --env-file .env.dev

# For production environment
npm run db -- --env-file .env
npx sequelize-cli db:seed:all --env-file .env
```
## Usage

### Development Mode

#### Running Individual Processors
```bash
# HL7 Message Processor
npm run hl7

# HL7 Raw Storage
npm run hl7store

# Patient Admission Processor
npm run patad

# Notification Generator
npm run notification

# Email Processor
npm run email

# SMS Processor
npm run text

# HR Data Processor
npm run hrdata
```

#### Running with Custom Queue
```bash
# Generic command for any queue
npm run dev -- --queue=your_queue_name

# Examples
npm run dev -- --queue=hl7_queue
npm run dev -- --queue=custom_processor_queue
```

### Production Deployment

#### PM2 Process Management
The system includes PM2 configuration for production deployment:

```bash
# Start all processors
pm2 start ecosystem.config.js

# Start specific processors
pm2 start ecosystem.config.js --only hl7_parser
pm2 start ecosystem.config.js --only patient_admission_procesor
pm2 start ecosystem.config.js --only email_procesor
pm2 start ecosystem.config.js --only notification_procesor
pm2 start ecosystem.config.js --only text_procesor
pm2 start ecosystem.config.js --only hr_data_processor

# Monitor processes
pm2 status
pm2 logs
pm2 monit
```

#### Docker Deployment
```dockerfile
# Example Dockerfile usage
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
CMD ["node", "index.js", "--queue=hl7_queue"]
```

### Database Management

#### Schema Management
```bash
# Sync database schema
npm run db

# Sync with specific environment
npm run db -- --env-file .env.dev

# Refresh database (drop and recreate)
npm run db:refresh

# Refresh specific models
npm run db:refresh -- --model Facility Floor Room
```

#### Migration Management
```bash
# Run all migrations
npx sequelize-cli db:migrate

# Undo last migration
npx sequelize-cli db:migrate:undo

# Undo all migrations
npx sequelize-cli db:migrate:undo:all
```

#### Seeder Management
```bash
# Run all seeders
npx sequelize-cli db:seed:all

# Run specific seeder
npx sequelize-cli db:seed --seed **************-function-seeder.js

# Undo all seeders
npx sequelize-cli db:seed:undo:all

# Undo specific seeder
npx sequelize-cli db:seed:undo --seed **************-function-seeder.js

# Environment-specific seeding
npx sequelize-cli db:seed:all --env-file .env.development
```

## Performance Monitoring

The system includes comprehensive performance monitoring capabilities:

### Performance Metrics Tracked
- **Message Processing Time**: Individual and batch processing times
- **Throughput**: Messages processed per second
- **Success Rate**: Percentage of successfully processed messages
- **Queue Performance**: Queue assertion and message sending times
- **Database Performance**: Query execution times
- **Cache Performance**: Cache hit/miss ratios and response times

### Performance Logs
Performance data is automatically logged to the `performances/` directory:

```
performances/
├── 2025-06-27T09-26-52-946Z_MessageCollection_hr_csv_data_collection_1_1751016412945.log
├── 2025-06-27T09-32-40-650Z_MessageCollection_hr_csv_data_collection_1_1751016760650.log
└── ...
```

### Performance Monitoring Features
- **Session-Based Tracking**: Each processing session gets unique performance ID
- **Real-Time Metrics**: Live performance data during processing
- **Historical Analysis**: Stored performance data for trend analysis
- **Alerting**: Performance threshold monitoring (configurable)

### Sample Performance Output
```json
{
  "sessionId": "MessageCollection_hr_csv_data_collection_1_1751016412945",
  "collectionId": "collection_1_1751016412945",
  "totalMessages": 100,
  "successful": 98,
  "failed": 2,
  "statistics": {
    "averageProcessingTimeMs": 45.67,
    "maxProcessingTimeMs": 120.34,
    "minProcessingTimeMs": 12.45,
    "successRate": 98.0,
    "throughputMessagesPerSecond": 22.5,
    "totalElapsedTimeMs": 4444.56
  }
}
```
## Deployment

### Production Environment Setup

#### 1. Environment Configuration
```bash
# Production environment variables
NODE_ENV=production
CONCURRENCY_LIMIT=25
COLLECTION_TIMEOUT=2000
SHUTDOWN_TIMEOUT=60000
LOG_LEVEL=warn
LOG_FILE_TRANSPORT=true
CACHE_DRIVER=redis
```

#### 2. PM2 Ecosystem Configuration
The `ecosystem.config.js` file defines production deployment settings:

```javascript
module.exports = {
  apps: [
    {
      name: 'hl7_parser',
      script: './index.js',
      cwd: '/opt/processor',
      args: '--queue=hl7_queue',
      instances: 2,  // Multiple instances for high availability
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production'
      }
    }
    // ... other processors
  ]
};
```

#### 3. Monitoring and Alerting
- **PM2 Monitoring**: Built-in process monitoring
- **Log Aggregation**: Centralized logging with Winston
- **Performance Monitoring**: Automated performance tracking
- **Health Checks**: Database and RabbitMQ connectivity monitoring

#### 4. Scaling Considerations
- **Horizontal Scaling**: Run multiple instances of each processor
- **Queue Partitioning**: Distribute load across multiple queues
- **Database Optimization**: Read replicas for high-read workloads
- **Caching Strategy**: Redis cluster for distributed caching

### Troubleshooting

#### Common Issues
1. **RabbitMQ Connection Issues**
   - Check RABBITMQ_URL configuration
   - Verify RabbitMQ server status
   - Check network connectivity

2. **Database Connection Issues**
   - Verify database credentials
   - Check database server status
   - Review connection pool settings

3. **Performance Issues**
   - Adjust CONCURRENCY_LIMIT
   - Optimize COLLECTION_TIMEOUT
   - Review cache configuration
   - Monitor database query performance

4. **Memory Issues**
   - Reduce CONCURRENCY_LIMIT
   - Implement message batching
   - Optimize cache TTL settings
   - Monitor garbage collection

#### Debug Mode
```bash
# Enable debug logging
LOG_LEVEL=debug npm run dev -- --queue=hl7_queue

# Enable database query logging
DB_LOGGING=true npm run dev -- --queue=hl7_queue
```

---

## Event Types and Flow

### Internal Events
Internal events are generated by the CareMate system itself and processed through the internal middleware:

#### Event Flow:
1. **Rule Engine Trigger**: System events trigger rules in the JSON rules engine
2. **Event Generation**: Rules generate events with specific parameters
3. **Queue Routing**: Events are routed to appropriate queues based on configuration
4. **Processing**: Internal middleware processes events with action tracking
5. **Completion**: Events are marked as completed in the `event_actions` table

#### Example Internal Event Flow:
```
New Appointment Created → Rule Engine → Add Default Guests Event → patient_admission_procesor Queue → addDefaultGuest Function → Guest Records Created
```

### External Events
External events come from outside systems (HL7, agent data, etc.) and are processed through the external middleware:

#### Event Flow:
1. **External Source**: Data received from external systems
2. **Queue Ingestion**: Raw data placed in appropriate queue
3. **Trace Creation**: External middleware creates comprehensive trace
4. **Processing**: Function processes and transforms data
5. **Database Storage**: Processed data stored in internal schema
6. **Trace Completion**: Processing trace marked as completed

#### Example External Event Flow:
```
HL7 Message → hl7_queue → hl7Processor Function → Patient/Appointment Data → Database Storage
```

### Event Tracing and Monitoring

#### Trace Components:
- **Trace ID**: Unique identifier for end-to-end tracking
- **Function Context**: Links traces to specific processor functions
- **Performance Metrics**: Detailed timing and performance data
- **Error Tracking**: Comprehensive error logging and stack traces
- **Audit Trail**: Complete audit trail for compliance and debugging

#### Middy Integration:
The system uses Middy middleware framework for:
- **Before Hooks**: Initialize tracing and performance monitoring
- **After Hooks**: Complete traces and update status
- **Error Hooks**: Handle errors and update trace status
- **Context Passing**: Pass trace context through processing pipeline

---

## Contributing

### Development Guidelines
1. **Follow Naming Conventions**: Use established patterns for files and functions
2. **Add Comprehensive Tests**: Include unit and integration tests for new processors
3. **Update Documentation**: Keep documentation current with configuration changes
4. **Error Handling**: Implement proper error handling and logging
5. **Performance Testing**: Test with various message volumes and types

### Adding New Processors

#### 1. Create Function File
```javascript
// functions/newProcessor.function.js
const logger = require("../config/logger");

const newProcessor = async (eventInstance, context) => {
  const traceId = context.trace_id;
  logger.info(`[TRACE ${traceId}] Starting new processor`);

  try {
    // Your processing logic here
    logger.info(`[TRACE ${traceId}] Completed new processor`);
  } catch (error) {
    logger.error(`[TRACE ${traceId}] Error in new processor: ${error.message}`);
    throw error;
  }
};

module.exports = newProcessor;
```

#### 2. Update Function Seeder
```javascript
// Add to seeders/**************-function-seeder.js
{
  function_id: uuidv4(),
  queue: "new_processor_queue",
  type: "0", // 0 = internal, 1 = external
  name: "newProcessor",
  display_name: "New Processor",
  application_type_id: appTypes[0]["application_type_id"],
  created_at: new Date(),
  updated_at: new Date(),
}
```

#### 3. Add PM2 Configuration
```javascript
// Add to ecosystem.config.js
{
  name: 'new_processor',
  script: './index.js',
  cwd: '/opt/processor',
  args: '--queue=new_processor_queue',
  env: {
    NODE_ENV: 'development'
  }
}
```

#### 4. Add NPM Script
```json
// Add to package.json scripts
"newprocessor": "nodemon index.js --queue=new_processor_queue"
```
