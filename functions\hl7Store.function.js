const models = require("../models");
const {
  sequelize,
  Hl7Message
} = models;
const logger = require("../config/logger");

const hl7Store = async (eventInstance, context) => {
  const traceId = context.trace_id;
  logger.info(`[TRACE ${traceId}] Starting HL7 raw storing`);

  const { transformedData, eventCodeDetails } = context.functionData;
  if (!transformedData || !eventCodeDetails) {
    logger.error(`[TRACE ${traceId}] No transformed data available`);
    throw new Error('No transformed data available');
  }

  const transaction = await sequelize.transaction();
  try {

    const { message_type, eventCode } = eventCodeDetails;
    const { PatientIdentifier: identifierData } = transformedData;

    const hl7MessageData = {
      mrn: identifierData.identifier_value,
      message_type: message_type,
      hdr: eventCode,
      message: eventInstance,
      processed_at: Date.now(),
      updated_by: context.function.function_id,
    }

    await Hl7Message.create(hl7MessageData, {
      transaction,
    });

    await transaction.commit();
    logger.info(`[TRACE ${traceId}] Completed HL7 raw store`);
  } catch (err) {
    await transaction.rollback();
    logger.error(`[TRACE ${traceId}] Transaction rolled back: ${err.message}`);
    throw err;
  }
};

module.exports = hl7Store;
