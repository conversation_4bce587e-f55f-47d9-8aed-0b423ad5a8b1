const models = require("../models");
const {
  sequelize,
  Patient,
  PatientIdentifier,
  Appointment,
  EventTrace,
  Hl7Message
} = models;
const logger = require("../config/logger");

const hl7Processor = async (eventInstance, context) => {
  const traceId = context.trace_id;
  logger.info(`[TRACE ${traceId}] Starting HL7 processing`);

  const { transformedData, eventCodeDetails } = context.functionData;
  if (!transformedData || !eventCodeDetails) {
    logger.error(`[TRACE ${traceId}] No transformed data available`);
    throw new Error('No transformed data available');
  }

  const transaction = await sequelize.transaction();
  try {
    const { message_type, eventCode, type: appointmentType, status: appointmentStatus } = eventCodeDetails;
    const { Patient: patientData, PatientIdentifier: identifierData, Appointment: apptData } = transformedData;

    patientData.updated_by = context.function.function_id;

    const hl7MessageData = {
      mrn: identifierData.identifier_value,
      message_type: message_type,
      hdr: eventCode,
      message: eventInstance,
      processed_at: Date.now(),
      updated_by: context.function.function_id,

    }

    await Hl7Message.create(hl7MessageData, {
      transaction,
    });

    // upsert Patient + Identifier
    let identifier = await PatientIdentifier.findOne({
      where: { identifier_value: identifierData.identifier_value },
      transaction,
    });

    let patient;
    if (identifier) {
      patient = await Patient.findByPk(identifier.patient_id, {
        transaction,
      });
      await patient.update(patientData, { transaction });
    } else {
      patient = await Patient.create(patientData, { transaction });
      identifierData.patient_id = patient.patient_id;
      identifierData.effective_from = new Date();
      identifierData.updated_by = context.function.function_id;
      identifier = await PatientIdentifier.create(identifierData, {
        transaction,
      });
    }

    // upsert Appointment  by hl7_appointment_id
    apptData.patient_id = patient.patient_id;
    apptData.type = appointmentType;
    if (appointmentStatus) apptData.status = appointmentStatus;
    apptData.updated_by = context.function.function_id;
    // Create an event trace for further events that will be triggered by json rule engine
    const eventTraceInstance = await EventTrace.create(
      {
        function_id: context.function.function_id,
      },
      { transaction }
    );
    const traceContext = eventTraceInstance.get({ plain: true });

    let appt = await Appointment.findOne({
      where: {
        patient_id: apptData.patient_id,
        hl7_appointment_id: apptData.hl7_appointment_id,
      },
      transaction,
    });
    if (appt) {
      // update any changed fields (excluding the hl7_appointment_id itself)
      await appt.update(apptData, { transaction, traceContext });
    } else {
      // create new appointment record
      appt = await Appointment.create(apptData, { transaction, traceContext });
    }

    await transaction.commit();
    logger.info(`[TRACE ${traceId}] Completed HL7 processing`);
  } catch (err) {
    await transaction.rollback();
    logger.error(`[TRACE ${traceId}] Transaction rolled back: ${err.message}`);
    throw err;
  }
};

module.exports = hl7Processor;
