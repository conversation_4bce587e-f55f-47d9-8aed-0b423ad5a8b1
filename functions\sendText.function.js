const config = require("../config/config");
const logger = require("../config/logger");
const { getCachedNotificationText } = require("../helpers/caching.helper");
const models = require("../models");
const ejs = require("ejs");

const { TELNYX_API_KEY, TELNYX_PHONE_NUMBER } = config.telnyx;
let telnyx;
async function boot() {
  try {
    // dynamic import inside an async fn — no top-level await!
    const { default: Telnyx } = await import("telnyx");
    telnyx = Telnyx(TELNYX_API_KEY);
  }
  catch (err) {
    logger.error("Failed to initialize Telnyx or start app", err);
    throw "Failed to initialize Telnyx or start app";
  }
}
boot();

/**
 * Send an text notification based on an event.
 *
 * @param {Object} eventInstance - The event payload.
 * @param {string} eventInstance.event_type - The type of event (e.g. "guestArrived").
 * @param {string|number} eventInstance.parent_id - FK to the related record.
 * @param {Object} eventInstance.notification - Notification metadata.
 * @param {string} eventInstance.notification.template - EJS template filename (without extension).
 * @param {string} eventInstance.notification.subject - Text subject line.
 * @param {Object} context - Execution context with tracing info.
 * @param {string} context.trace_id - Unique ID for correlating logs.
 * @returns {Promise<void>} Resolves when the text is sent or skips if data is missing.
 * @throws {Error} If a database lookup or the sendText call fails.
 */
async function sendText(eventInstance, context) {
  const { event_type, notification, parent_id } = eventInstance
  const { trace_id } = context;
  logger.info(`[TRACE ${trace_id}] sendText invoked for event "${event_type}"`);
  const { notification_child_id, schema, schema_column } = notification;
  try {
    if (!notification_child_id && !schema && !schema_column) throw "Insufficient data supplied"

    const notificationText = await getCachedNotificationText(notification_child_id);
    if (!notificationText) throw `NotificationText "${notification.notification_child_id}" not found`;

    const { receiver_column, template } = notificationText;
    const Model = models[schema];
    if (!Model) throw `Model "${modelName}" not found`;
    const templateData = await Model.findOne({ where: { [schema_column]: parent_id } })
    const content = ejs.render(template, templateData);
    const to = templateData[receiver_column];

    await telnyx.messages.create({
      from: TELNYX_PHONE_NUMBER,
      to,
      text: content,
    });

    logger.info(`[TRACE ${trace_id}] Text successfully sent to ${to}`);
  } catch (err) {
    logger.error(`[TRACE ${trace_id}] sendText error: ${err.message}`);
    throw err;
  }
}

module.exports = sendText;
