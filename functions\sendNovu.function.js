const logger = require("../config/logger");
const config = require("../config/config");

/**
 * Send a notification via Novu based on event data.
 *
 * @param {Object} event - The event instance containing notification data.
 * @param {string} event.event_id - Unique identifier for the event.
 * @param {string|number} event.parent_id - ID of the parent record.
 * @param {Object} event.notification - Notification configuration.
 * @param {string} event.notification.notification_name - Name of the notification.
 * @param {Array} event.notification.channels - Channels to send notification to.
 * @param {Object} event.params - Event parameters including data for the notification.
 * @param {Object} context - Execution context with tracing information.
 * @param {string} context.trace_id - Unique trace identifier for logging.
 * @returns {Promise<void>} Resolves when the notification has been sent.
 * @throws {Error} If Novu API call fails or required data is missing.
 */
const sendNovu = async (event, context) => {
  const traceId = context.trace_id || 'unknown';
  logger.info(`[TRACE ${traceId}] Starting Novu notification processing`);

  try {
    // Check if Novu is configured
    if (!config.novu || !config.novu.apiKey) {
      throw new Error('Novu API key is not configured');
    }
    
    // Novu API configuration
    const novuApiUrl = 'https://api.novu.co/v1/events/trigger';
    const novuApiKey = config.novu.apiKey;
    
    // Extract notification details
    const { notification_name, channels } = event.notification || {};
    const templateData = event.params.data || {};
    
    if (!notification_name) {
      throw new Error('Missing notification_name in event');
    }
    
    // Get subscriber ID from the event data
    const subscriberId = templateData.user_id || 
                         templateData.patient_id || 
                         templateData.subscriber_id || 
                         `default-${event.parent_id}`;
    
    // Map notification_name to Novu workflow ID (kebab-case)
    const workflowId = notification_name.replace(/\s+/g, '-').toLowerCase();
    
    // Prepare payload with all available data
    const payload = {
      ...templateData,
      event_type: event.event_type,
      parent_id: event.parent_id,
      trace_id: traceId,
      channels: channels || ['email', 'sms', 'in_app']
    };
    
    // Prepare subscriber data for the trigger call
    const subscriberData = {
      subscriberId: subscriberId,
    };

    // Add email and other subscriber details if available
    if (templateData.email) {
      subscriberData.email = templateData.email;
    }
    if (templateData.first_name || templateData.firstName) {
      subscriberData.firstName = templateData.first_name || templateData.firstName;
    }
    if (templateData.last_name || templateData.lastName) {
      subscriberData.lastName = templateData.last_name || templateData.lastName;
    }
    if (templateData.phone) {
      subscriberData.phone = templateData.phone;
    }

    logger.info(`[TRACE ${traceId}] Prepared subscriber data for ${subscriberId}`);
    
    // Trigger the notification
    const transactionId = `${event.event_id}_${Date.now()}`;
    logger.info(`[TRACE ${traceId}] Triggering Novu workflow: ${workflowId} for subscriber: ${subscriberId}`);

    // Prepare REST API request
    const requestPayload = {
      name: workflowId,
      to: subscriberData,
      payload: payload,
    };

    // Make REST API call to Novu
    const response = await fetch(novuApiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `ApiKey ${novuApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestPayload)
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(`Novu API error: ${response.status} - ${JSON.stringify(result)}`);
    }

    logger.info(`[TRACE ${traceId}] Novu notification sent successfully`);
    logger.info(`[TRACE ${traceId}] Novu response: ${JSON.stringify(result)}`);
    logger.info(`[TRACE ${traceId}] Transaction ID: ${result.transactionId || 'N/A'}`);
    
  } catch (error) {
    logger.error(`[TRACE ${traceId}] Error sending Novu notification: ${error.message}`);
    throw error;
  }
};

module.exports = sendNovu;

