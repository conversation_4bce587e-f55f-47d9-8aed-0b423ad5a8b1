const dtlTemplate = require("../mappings/hl7Message.dtl.json");
const { transformWithDTL, getMasterDataGroupMap } = require("../helpers/hl7.helper");
const models = require("../models");

const groupMap = getMasterDataGroupMap(models);

// Sample HL7 data
const testData = {
    "PID.5.1": "<PERSON>",
    "PID.5.2": "<PERSON>",
    "PID.7.1": "19850315",
    "PID.8": "M",
    "PID.3.1": "12345",
    "PV1.44.1": "20250418045253",
    "PV1.7.2": "BILLU",
    "PV1.7.3": "UNIVERSE",

};

(async function testDTL() {
    try {
        const result = await transformWithDTL(testData, dtlTemplate, groupMap);
        console.log('Transformation successful:', result);
    } catch (error) {
        console.error('Transformation failed:', error);
    }
})();

