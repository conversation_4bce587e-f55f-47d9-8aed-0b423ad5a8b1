const fs = require("fs");
const path = require("path");
const functions = {};

// Get the current directory where the function files reside.
const functionsDirectory = __dirname;

// Read all files in the functions directory that end with ".function.js", excluding this index file.
fs.readdirSync(functionsDirectory)
  .filter((file) => file.endsWith(".function.js") && file !== "index.js")
  .forEach((file) => {
    // Use titleCase (or any naming convention) to set the function name.
    const functionName = path.basename(file, ".function.js");
    const functionModule = require(path.join(functionsDirectory, file));
    functions[functionName] = functionModule;
  });
  
// console.log(functions)

module.exports = functions;
